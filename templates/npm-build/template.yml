spec:
  inputs:
    build-name:
    build-number:
    build-stage:
      default: build
    node-image-name:
      default: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/nodejs:22-latest
    jfrog-project:
      default: bint
    stage-publish-build-info:
      default: post-test
    enable-gitlab-publish:          
      default: "true"               
---
include:
  - local: "/templates/.jfrog.gitlab-ci.yml"

npm-build:
  variables:
    KUBERNETES_MEMORY_LIMIT: 4Gi
    KUBERNETES_EPHEMERAL_STORAGE_LIMIT: 8Gi
  stage: $[[ inputs.build-stage ]]
  extends: [.jf_npm]
  rules:
    - if: $CI_COMMIT_BRANCH || $CI_PIPELINE_SOURCE == "merge_request_event"
  image: $[[ inputs.node-image-name ]]
  script:
    - |
      if [ ! -d "node_modules" ]; then
        jf npm ci --build-name="${BUILD_NAME}" --build-number="${BUILD_NUMBER}" ;
      fi
    - jf npm run build --build-name=$[[ inputs.build-name ]] --build-number=$[[ inputs.build-number ]]


npm-build-publish:
  stage: $[[ inputs.build-stage ]]
  image: $[[ inputs.node-image-name ]]
  extends: [.jf_npm]
  variables:
    KUBERNETES_MEMORY_LIMIT: 4Gi
    KUBERNETES_EPHEMERAL_STORAGE_LIMIT: 5Gi
  rules:
    - if: $CI_COMMIT_TAG
  script:
    - |
      if [ ! -d "node_modules" ]; then
        jf npm ci --build-name="${BUILD_NAME}" --build-number="${BUILD_NUMBER}" ;
      fi
    - jf npm run build --build-name=$[[ inputs.build-name ]] --build-number=$[[ inputs.build-number ]]
    - jf npm publish --build-name=$[[ inputs.build-name ]] --build-number=$[[ inputs.build-number ]] --project=$[[ inputs.jfrog-project ]]
    # Also publish to GitLab Package Registry (parallel to Artifactory)
    - |
      if [ "$[[ inputs.enable-gitlab-publish ]]" = "true" ]; then
        echo "Publishing to GitLab Package Registry..."

        # Determine folder based on tag pattern
        GITLAB_FOLDER=""
        if [[ "$CI_COMMIT_TAG" =~ $ALPHA_PATTERN ]]; then
          GITLAB_FOLDER="dev"
          echo "Alpha tag detected - publishing to dev folder"
        elif [[ "$CI_COMMIT_TAG" =~ $BETA_PATTERN ]] || [[ "$CI_COMMIT_TAG" != *"alpha"* && "$CI_COMMIT_TAG" != *"rc"* ]]; then
          GITLAB_FOLDER="test"
          echo "RC or release tag detected - publishing to test folder"
        else
          GITLAB_FOLDER="dev"
          echo "Default - publishing to dev folder"
        fi

        # Backup original .npmrc
        cp .npmrc .npmrc.backup || true

        # Configure GitLab registry with folder
        echo "@dfe:registry=https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/${GITLAB_FOLDER}/" >> .npmrc
        echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/${GITLAB_FOLDER}/:_authToken=${CI_JOB_TOKEN}" >> .npmrc

        # Publish to GitLab Package Registry with folder
        npm publish --registry=https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/${GITLAB_FOLDER}/ || echo "GitLab publish failed, continuing..."

        # Restore original .npmrc
        mv .npmrc.backup .npmrc || true

        echo "Published to GitLab Package Registry folder: ${GITLAB_FOLDER}"
      else
        echo "GitLab publishing disabled"
      fi

npm-publish-jfrog-build-info:
  stage: $[[ inputs.stage-publish-build-info ]]
  image: $[[ inputs.node-image-name ]]
  extends: [.jf_npm]
  variables:
    BUILD_NAME: $[[ inputs.build-name ]]
    BUILD_NUMBER: $[[ inputs.build-number ]]
  rules:
    - if: $CI_COMMIT_TAG
  script:
    - !reference [.setup_jfrog, script]
    - jf rt build-publish $BUILD_NAME $BUILD_NUMBER --project=$[[ inputs.jfrog-project ]]

npm-publish-gitlab-build-info:
  stage: $[[ inputs.stage-publish-build-info ]]
  image: $[[ inputs.node-image-name ]]
  variables:
    BUILD_NAME: $[[ inputs.build-name ]]
    BUILD_NUMBER: $[[ inputs.build-number ]]
    ENABLE_GITLAB_PUBLISH: $[[ inputs.enable-gitlab-publish ]]
  rules:
    - if: $CI_COMMIT_TAG && $ENABLE_GITLAB_PUBLISH == "true"
  script:
    - echo "Publishing npm build info to GitLab registry"
    - echo "Build Name - $BUILD_NAME"
    - echo "Build Number - $BUILD_NUMBER"
    - echo "Package - $CI_PROJECT_NAME"
    - echo "Registry - $CI_API_V4_URL/projects/$CI_PROJECT_ID/packages/npm"

npm-publish-prod-manual:
  stage: $[[ inputs.stage-publish-build-info ]]
  image: $[[ inputs.node-image-name ]]
  extends: [.jf_npm]
  variables:
    KUBERNETES_MEMORY_LIMIT: 4Gi
    KUBERNETES_EPHEMERAL_STORAGE_LIMIT: 5Gi
    BUILD_NAME: $[[ inputs.build-name ]]
    BUILD_NUMBER: $[[ inputs.build-number ]]
    GITLAB_FOLDER: "prod"
  rules:
    - if: $CI_COMMIT_TAG && $[[ inputs.enable-gitlab-publish ]] == "true"
      when: manual
      allow_failure: true
  script:
    - echo "Manual production publishing to GitLab Package Registry..."
    - echo "Publishing package to prod folder"
    - echo "Tag: $CI_COMMIT_TAG"
    - echo "Project: $CI_PROJECT_NAME"

    # Ensure we have the built package
    - |
      if [ ! -d "node_modules" ]; then
        jf npm ci --build-name="${BUILD_NAME}" --build-number="${BUILD_NUMBER}" ;
      fi
    - jf npm run build --build-name=$[[ inputs.build-name ]] --build-number=$[[ inputs.build-number ]]

    # Backup original .npmrc
    - cp .npmrc .npmrc.backup || true

    # Configure GitLab registry for prod folder
    - echo "@dfe:registry=https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/${GITLAB_FOLDER}/" >> .npmrc
    - echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/${GITLAB_FOLDER}/:_authToken=${CI_JOB_TOKEN}" >> .npmrc

    # Publish to GitLab Package Registry prod folder
    - npm publish --registry=https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/${GITLAB_FOLDER}/

    # Restore original .npmrc
    - mv .npmrc.backup .npmrc || true

    - echo "Successfully published to GitLab Package Registry prod folder"

