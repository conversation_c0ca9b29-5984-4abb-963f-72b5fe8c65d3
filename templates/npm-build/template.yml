spec:
  inputs:
    build-name:
    build-number:
    build-stage:
      default: build
    node-image-name:
      default: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/nodejs:22-latest
    jfrog-project:
      default: bint
    stage-publish-build-info:
      default: post-test
    enable-gitlab-publish:          
      default: "true"               
---
include:
  - local: "/templates/.jfrog.gitlab-ci.yml"

npm-build:
  variables:
    KUBERNETES_MEMORY_LIMIT: 4Gi
    KUBERNETES_EPHEMERAL_STORAGE_LIMIT: 8Gi
  stage: $[[ inputs.build-stage ]]
  extends: [.jf_npm]
  rules:
    - if: $CI_COMMIT_BRANCH || $CI_PIPELINE_SOURCE == "merge_request_event"
  image: $[[ inputs.node-image-name ]]
  script:
    - |
      if [ ! -d "node_modules" ]; then
        jf npm ci --build-name="${BUILD_NAME}" --build-number="${BUILD_NUMBER}" ;
      fi
    - jf npm run build --build-name=$[[ inputs.build-name ]] --build-number=$[[ inputs.build-number ]]


npm-build-publish:
  stage: $[[ inputs.build-stage ]]
  image: $[[ inputs.node-image-name ]]
  extends: [.jf_npm]
  variables:
    KUBERNETES_MEMORY_LIMIT: 4Gi
    KUBERNETES_EPHEMERAL_STORAGE_LIMIT: 5Gi
  rules:
    - if: $CI_COMMIT_TAG
  script:
    - |
      if [ ! -d "node_modules" ]; then
        jf npm ci --build-name="${BUILD_NAME}" --build-number="${BUILD_NUMBER}" ;
      fi
    - jf npm run build --build-name=$[[ inputs.build-name ]] --build-number=$[[ inputs.build-number ]]
    - jf npm publish --build-name=$[[ inputs.build-name ]] --build-number=$[[ inputs.build-number ]] --project=$[[ inputs.jfrog-project ]]
    # Also publish to GitLab Package Registry (parallel to Artifactory)
    - |
      if [ "$[[ inputs.enable-gitlab-publish ]]" = "true" ]; then
        echo "Publishing to GitLab Package Registry..."

        # Determine environment suffix based on tag pattern
        ENV_SUFFIX=""
        if [[ "$CI_COMMIT_TAG" =~ $ALPHA_PATTERN ]]; then
          ENV_SUFFIX="-dev"
          echo "Alpha tag detected - publishing with dev suffix"
        elif [[ "$CI_COMMIT_TAG" =~ $BETA_PATTERN ]] || [[ "$CI_COMMIT_TAG" != *"alpha"* && "$CI_COMMIT_TAG" != *"rc"* ]]; then
          ENV_SUFFIX="-test"
          echo "RC or release tag detected - publishing with test suffix"
        else
          ENV_SUFFIX="-dev"
          echo "Default - publishing with dev suffix"
        fi

        # Backup original package.json and .npmrc
        cp package.json package.json.backup
        cp .npmrc .npmrc.backup || true

        # Get original package name and modify it with environment suffix
        ORIGINAL_NAME=$(node -p "require('./package.json').name")
        NEW_NAME="${ORIGINAL_NAME}${ENV_SUFFIX}"
        echo "Original package name - ${ORIGINAL_NAME}"
        echo "New package name - ${NEW_NAME}"

        # Update package.json with new name
        node -e "
          const pkg = require('./package.json');
          pkg.name = '${NEW_NAME}';
          require('fs').writeFileSync('package.json', JSON.stringify(pkg, null, 2));
        "

        # Configure GitLab registry
        echo "@dfe:registry=https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/" >> .npmrc
        echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}" >> .npmrc

        # Publish to GitLab Package Registry with modified name
        npm publish --registry=https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/ || echo "GitLab publish failed, continuing..."

        # Restore original package.json and .npmrc
        mv package.json.backup package.json
        mv .npmrc.backup .npmrc || true

        echo "Published to GitLab Package Registry with name - ${NEW_NAME}"
      else
        echo "GitLab publishing disabled"
      fi

npm-publish-jfrog-build-info:
  stage: $[[ inputs.stage-publish-build-info ]]
  image: $[[ inputs.node-image-name ]]
  extends: [.jf_npm]
  variables:
    BUILD_NAME: $[[ inputs.build-name ]]
    BUILD_NUMBER: $[[ inputs.build-number ]]
  rules:
    - if: $CI_COMMIT_TAG
  script:
    - !reference [.setup_jfrog, script]
    - jf rt build-publish $BUILD_NAME $BUILD_NUMBER --project=$[[ inputs.jfrog-project ]]

npm-publish-gitlab-build-info:
  stage: $[[ inputs.stage-publish-build-info ]]
  image: $[[ inputs.node-image-name ]]
  variables:
    BUILD_NAME: $[[ inputs.build-name ]]
    BUILD_NUMBER: $[[ inputs.build-number ]]
    ENABLE_GITLAB_PUBLISH: $[[ inputs.enable-gitlab-publish ]]
  rules:
    - if: $CI_COMMIT_TAG && $ENABLE_GITLAB_PUBLISH == "true"
  script:
    - echo "Publishing npm build info to GitLab registry"
    - echo "Build Name - $BUILD_NAME"
    - echo "Build Number - $BUILD_NUMBER"
    - echo "Package - $CI_PROJECT_NAME"
    - echo "Registry - $CI_API_V4_URL/projects/$CI_PROJECT_ID/packages/npm"

npm-publish-prod-manual:
  stage: $[[ inputs.stage-publish-build-info ]]
  image: $[[ inputs.node-image-name ]]
  extends: [.jf_npm]
  variables:
    KUBERNETES_MEMORY_LIMIT: 4Gi
    KUBERNETES_EPHEMERAL_STORAGE_LIMIT: 5Gi
    BUILD_NAME: $[[ inputs.build-name ]]
    BUILD_NUMBER: $[[ inputs.build-number ]]
  rules:
    - if: $CI_COMMIT_TAG && "$[[ inputs.enable-gitlab-publish ]]" == "true"
      when: manual
      allow_failure: true
  script:
    - echo "Manual production publishing to GitLab Package Registry..."
    - echo "Publishing package with prod suffix"
    - echo "Tag - $CI_COMMIT_TAG"
    - echo "Project - $CI_PROJECT_NAME"

    # Ensure we have the built package
    - |
      if [ ! -d "node_modules" ]; then
        jf npm ci --build-name="${BUILD_NAME}" --build-number="${BUILD_NUMBER}" ;
      fi
    - jf npm run build --build-name=$[[ inputs.build-name ]] --build-number=$[[ inputs.build-number ]]

    # Backup original package.json and .npmrc
    - cp package.json package.json.backup
    - cp .npmrc .npmrc.backup || true

    # Get original package name and modify it with prod suffix
    - ORIGINAL_NAME=$(node -p "require('./package.json').name")
    - NEW_NAME="${ORIGINAL_NAME}-prod"
    - echo "Original package name - ${ORIGINAL_NAME}"
    - echo "New package name - ${NEW_NAME}"

    # Update package.json with new name
    - |
      node -e "
        const pkg = require('./package.json');
        pkg.name = '${NEW_NAME}';
        require('fs').writeFileSync('package.json', JSON.stringify(pkg, null, 2));
      "

    # Configure GitLab registry
    - echo "@dfe:registry=https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/" >> .npmrc
    - echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}" >> .npmrc

    # Publish to GitLab Package Registry with prod suffix
    - npm publish --registry=https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/

    # Restore original package.json and .npmrc
    - mv package.json.backup package.json
    - mv .npmrc.backup .npmrc || true

    - echo "Successfully published to GitLab Package Registry with name - ${NEW_NAME}"

